2025-08-01 13:45:45,872 INFO     25 ragflow_server log path: /ragflow/logs/ragflow_server.log, log levels: {'peewee': 'WARNING', 'pdfminer': 'WARNING', 'root': 'INFO'}
2025-08-01 13:45:46,570 INFO     25 can't import package 'torch'
2025-08-01 13:45:51,266 INFO     25 init database on cluster mode successfully
2025-08-01 13:45:54,736 INFO     25 load_model /ragflow/rag/res/deepdoc/det.onnx uses CPU
2025-08-01 13:45:54,825 INFO     25 load_model /ragflow/rag/res/deepdoc/rec.onnx uses CPU
2025-08-01 13:46:00,051 INFO     25 
        ____   ___    ______ ______ __               
       / __ \ /   |  / ____// ____// /____  _      __
      / /_/ // /| | / / __ / /_   / // __ \| | /| / /
     / _, _// ___ |/ /_/ // __/  / // /_/ /| |/ |/ / 
    /_/ |_|/_/  |_|\____//_/    /_/ \____/ |__/|__/                             

    
2025-08-01 13:46:00,052 INFO     25 RAGFlow version: v0.19.0 slim
2025-08-01 13:46:00,052 INFO     25 project base: /ragflow
2025-08-01 13:46:00,052 INFO     25 Current configs, from /ragflow/conf/service_conf.yaml:
	ragflow: {'host': '0.0.0.0', 'http_port': 9380}
	mysql: {'name': 'rag_flow', 'user': 'root', 'password': '********', 'host': 'mysql', 'port': 3306, 'max_connections': 900, 'stale_timeout': 300}
	minio: {'user': 'rag_flow', 'password': '********', 'host': 'minio:9000'}
	es: {'hosts': 'http://es01:9200', 'username': 'elastic', 'password': '********'}
	os: {'hosts': 'http://opensearch01:9201', 'username': 'admin', 'password': '********'}
	infinity: {'uri': 'infinity:23817', 'db_name': 'default_db'}
	redis: {'db': 1, 'password': '********', 'host': 'redis:6379'}
2025-08-01 13:46:00,052 INFO     25 Use Elasticsearch http://es01:9200 as the doc engine.
2025-08-01 13:46:00,119 INFO     25 GET http://es01:9200/ [status:200 duration:0.065s]
2025-08-01 13:46:00,123 INFO     25 HEAD http://es01:9200/ [status:200 duration:0.004s]
2025-08-01 13:46:00,124 INFO     25 Elasticsearch http://es01:9200 is healthy.
2025-08-01 13:46:00,126 WARNING  25 Load term.freq FAIL!
2025-08-01 13:46:00,128 WARNING  25 Realtime synonym is disabled, since no redis connection.
2025-08-01 13:46:00,130 WARNING  25 Load term.freq FAIL!
2025-08-01 13:46:00,132 WARNING  25 Realtime synonym is disabled, since no redis connection.
2025-08-01 13:46:00,132 INFO     25 MAX_CONTENT_LENGTH: 134217728
2025-08-01 13:46:00,132 INFO     25 MAX_FILE_COUNT_PER_USER: 0
2025-08-01 13:46:06,637 INFO     25 init web data success:2.366532564163208
2025-08-01 13:46:06,638 INFO     25 Recursively importing plugins from path `/ragflow/plugin/embedded_plugins`
2025-08-01 13:46:06,639 INFO     25 Loaded llm_tools plugin BadCalculatorPlugin version 1.0.0
2025-08-01 13:46:06,640 INFO     25 update_progress lock_value: 549d25a4-28a5-4364-98b0-e032ec721371
2025-08-01 13:46:06,640 INFO     25 RAGFlow HTTP server start...
2025-08-01 13:46:06,643 INFO     25 [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:9380
 * Running on http://**********:9380
2025-08-01 13:46:06,643 INFO     25 [33mPress CTRL+C to quit[0m
2025-08-01 13:46:37,153 ERROR    25 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 854, in dispatch_request
    self.raise_routing_exception(req)
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 463, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "/ragflow/.venv/lib/python3.10/site-packages/werkzeug/routing/map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-08-01 13:46:37,158 INFO     25 172.19.0.1 - - [01/Aug/2025 13:46:37] "GET / HTTP/1.1" 200 -
2025-08-01 13:46:42,038 ERROR    25 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 854, in dispatch_request
    self.raise_routing_exception(req)
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 463, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "/ragflow/.venv/lib/python3.10/site-packages/werkzeug/routing/map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-08-01 13:46:42,039 INFO     25 172.19.0.1 - - [01/Aug/2025 13:46:42] "GET / HTTP/1.1" 200 -
2025-08-01 13:46:42,336 ERROR    25 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 854, in dispatch_request
    self.raise_routing_exception(req)
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 463, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "/ragflow/.venv/lib/python3.10/site-packages/werkzeug/routing/map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-08-01 13:46:42,337 INFO     25 172.19.0.1 - - [01/Aug/2025 13:46:42] "GET /favicon.ico HTTP/1.1" 200 -
2025-08-01 13:46:48,899 ERROR    25 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 854, in dispatch_request
    self.raise_routing_exception(req)
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 463, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "/ragflow/.venv/lib/python3.10/site-packages/werkzeug/routing/map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-08-01 13:46:48,900 INFO     25 172.19.0.1 - - [01/Aug/2025 13:46:48] "GET /v1/api/health HTTP/1.1" 200 -
2025-08-01 13:47:00,175 ERROR    25 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 854, in dispatch_request
    self.raise_routing_exception(req)
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 463, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "/ragflow/.venv/lib/python3.10/site-packages/werkzeug/routing/map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-08-01 13:47:00,176 INFO     25 172.19.0.1 - - [01/Aug/2025 13:47:00] "GET /v1/datasets HTTP/1.1" 200 -
2025-08-01 13:47:40,260 ERROR    25 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 854, in dispatch_request
    self.raise_routing_exception(req)
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 463, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "/ragflow/.venv/lib/python3.10/site-packages/werkzeug/routing/map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-08-01 13:47:40,261 INFO     25 172.19.0.1 - - [01/Aug/2025 13:47:40] "GET / HTTP/1.1" 200 -
2025-08-01 13:47:40,611 ERROR    25 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 854, in dispatch_request
    self.raise_routing_exception(req)
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 463, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "/ragflow/.venv/lib/python3.10/site-packages/werkzeug/routing/map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-08-01 13:47:40,612 INFO     25 172.19.0.1 - - [01/Aug/2025 13:47:40] "GET /favicon.ico HTTP/1.1" 200 -
2025-08-01 06:11:07,750 INFO     25 ragflow_server log path: /ragflow/logs/ragflow_server.log, log levels: {'peewee': 'WARNING', 'pdfminer': 'WARNING', 'root': 'INFO'}
2025-08-01 06:11:08,403 INFO     25 can't import package 'torch'
2025-08-01 06:11:12,864 INFO     25 init database on cluster mode successfully
2025-08-01 06:11:16,505 INFO     25 load_model /ragflow/rag/res/deepdoc/det.onnx uses CPU
2025-08-01 06:11:16,603 INFO     25 load_model /ragflow/rag/res/deepdoc/rec.onnx uses CPU
2025-08-01 06:11:21,760 INFO     25 
        ____   ___    ______ ______ __               
       / __ \ /   |  / ____// ____// /____  _      __
      / /_/ // /| | / / __ / /_   / // __ \| | /| / /
     / _, _// ___ |/ /_/ // __/  / // /_/ /| |/ |/ / 
    /_/ |_|/_/  |_|\____//_/    /_/ \____/ |__/|__/                             

    
2025-08-01 06:11:21,761 INFO     25 RAGFlow version: v0.19.0 slim
2025-08-01 06:11:21,761 INFO     25 project base: /ragflow
2025-08-01 06:11:21,761 INFO     25 Current configs, from /ragflow/conf/service_conf.yaml:
	ragflow: {'host': '0.0.0.0', 'http_port': 9380}
	mysql: {'name': 'rag_flow', 'user': 'root', 'password': '********', 'host': 'mysql', 'port': 3306, 'max_connections': 900, 'stale_timeout': 300}
	minio: {'user': 'rag_flow', 'password': '********', 'host': 'minio:9000'}
	es: {'hosts': 'http://es01:9200', 'username': 'elastic', 'password': '********'}
	os: {'hosts': 'http://opensearch01:9201', 'username': 'admin', 'password': '********'}
	infinity: {'uri': 'infinity:23817', 'db_name': 'default_db'}
	redis: {'db': 1, 'password': '********', 'host': 'redis:6379'}
2025-08-01 06:11:21,762 INFO     25 Use Elasticsearch http://es01:9200 as the doc engine.
2025-08-01 06:11:21,770 INFO     25 GET http://es01:9200/ [status:200 duration:0.007s]
2025-08-01 06:11:21,773 INFO     25 HEAD http://es01:9200/ [status:200 duration:0.002s]
2025-08-01 06:11:21,773 INFO     25 Elasticsearch http://es01:9200 is healthy.
2025-08-01 06:11:21,776 WARNING  25 Load term.freq FAIL!
2025-08-01 06:11:21,778 WARNING  25 Realtime synonym is disabled, since no redis connection.
2025-08-01 06:11:21,780 WARNING  25 Load term.freq FAIL!
2025-08-01 06:11:21,782 WARNING  25 Realtime synonym is disabled, since no redis connection.
2025-08-01 06:11:21,782 INFO     25 MAX_CONTENT_LENGTH: 134217728
2025-08-01 06:11:21,782 INFO     25 MAX_FILE_COUNT_PER_USER: 0
2025-08-01 06:11:24,141 INFO     25 init web data success:2.3152506351470947
2025-08-01 06:11:24,142 INFO     25 Recursively importing plugins from path `/ragflow/plugin/embedded_plugins`
2025-08-01 06:11:24,143 INFO     25 Loaded llm_tools plugin BadCalculatorPlugin version 1.0.0
2025-08-01 06:11:24,143 INFO     25 update_progress lock_value: 987e5e9b-8320-4d7c-afa2-80b8797828cc
2025-08-01 06:11:24,144 INFO     25 RAGFlow HTTP server start...
2025-08-01 06:11:24,146 INFO     25 [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:9380
 * Running on http://**********:9380
2025-08-01 06:11:24,147 INFO     25 [33mPress CTRL+C to quit[0m
2025-08-01 06:11:34,450 ERROR    25 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 854, in dispatch_request
    self.raise_routing_exception(req)
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 463, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "/ragflow/.venv/lib/python3.10/site-packages/werkzeug/routing/map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-08-01 06:11:34,451 INFO     25 172.18.0.1 - - [01/Aug/2025 06:11:34] "GET / HTTP/1.1" 200 -
2025-08-01 06:11:53,421 ERROR    25 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 854, in dispatch_request
    self.raise_routing_exception(req)
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 463, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "/ragflow/.venv/lib/python3.10/site-packages/werkzeug/routing/map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-08-01 06:11:53,422 INFO     25 172.18.0.1 - - [01/Aug/2025 06:11:53] "GET / HTTP/1.1" 200 -
2025-08-01 06:14:58,071 ERROR    25 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 854, in dispatch_request
    self.raise_routing_exception(req)
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 463, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "/ragflow/.venv/lib/python3.10/site-packages/werkzeug/routing/map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-08-01 06:14:58,073 INFO     25 172.18.0.1 - - [01/Aug/2025 06:14:58] "GET / HTTP/1.1" 200 -
2025-08-01 06:14:58,319 ERROR    25 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 854, in dispatch_request
    self.raise_routing_exception(req)
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 463, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "/ragflow/.venv/lib/python3.10/site-packages/werkzeug/routing/map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-08-01 06:14:58,320 INFO     25 172.18.0.1 - - [01/Aug/2025 06:14:58] "GET /favicon.ico HTTP/1.1" 200 -
2025-08-01 06:20:13,319 INFO     25 172.18.0.1 - - [01/Aug/2025 06:20:13] "GET /v1/user/login/channels HTTP/1.1" 200 -
2025-08-01 06:20:13,321 INFO     25 172.18.0.1 - - [01/Aug/2025 06:20:13] "GET /v1/system/config HTTP/1.1" 200 -
2025-08-01 06:21:03,865 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:03] "GET /v1/user/login/channels HTTP/1.1" 200 -
2025-08-01 06:21:03,867 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:03] "GET /v1/system/config HTTP/1.1" 200 -
2025-08-01 06:21:22,357 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:22] "GET /v1/user/login/channels HTTP/1.1" 200 -
2025-08-01 06:21:22,683 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:22] "GET /v1/system/config HTTP/1.1" 200 -
2025-08-01 06:21:36,107 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:36] "POST /v1/user/register HTTP/1.1" 200 -
2025-08-01 06:21:38,440 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:38] "POST /v1/user/login HTTP/1.1" 200 -
2025-08-01 06:21:38,897 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:38] "POST /v1/kb/list?page=1&page_size=30&keywords= HTTP/1.1" 200 -
2025-08-01 06:21:39,033 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:39] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 06:21:39,086 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:39] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 06:21:39,116 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:39] "GET /v1/tenant/list HTTP/1.1" 200 -
2025-08-01 06:21:43,210 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:43] "POST /v1/kb/list HTTP/1.1" 200 -
2025-08-01 06:21:43,229 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:43] "GET /v1/file/list?parent_id=&keywords=&page_size=10&page=1 HTTP/1.1" 200 -
2025-08-01 06:21:43,526 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:43] "GET /v1/canvas/listteam?page=1&page_size=30&keywords= HTTP/1.1" 200 -
2025-08-01 06:21:43,526 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:43] "GET /v1/canvas/templates HTTP/1.1" 200 -
2025-08-01 06:21:44,304 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:44] "POST /v1/kb/list HTTP/1.1" 200 -
2025-08-01 06:21:44,324 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:44] "GET /v1/file/list?parent_id=&keywords=&page_size=10&page=1 HTTP/1.1" 200 -
2025-08-01 06:21:45,102 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:45] "GET /v1/canvas/listteam?page=1&page_size=30&keywords= HTTP/1.1" 200 -
2025-08-01 06:21:45,104 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:45] "GET /v1/canvas/templates HTTP/1.1" 200 -
2025-08-01 06:21:45,389 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:45] "POST /v1/kb/list HTTP/1.1" 200 -
2025-08-01 06:21:46,662 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:46] "GET /v1/canvas/templates HTTP/1.1" 200 -
2025-08-01 06:21:46,664 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:46] "GET /v1/canvas/listteam?page=1&page_size=30&keywords= HTTP/1.1" 200 -
2025-08-01 06:21:46,915 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:46] "POST /v1/kb/list HTTP/1.1" 200 -
2025-08-01 06:21:47,238 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:47] "GET /v1/file/list?parent_id=&keywords=&page_size=10&page=1 HTTP/1.1" 200 -
2025-08-01 06:21:48,602 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:48] "GET /v1/file/all_parent_folder?file_id=caf039566e9f11f0aa3a7e3f2c089143 HTTP/1.1" 200 -
2025-08-01 06:21:48,609 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:48] "GET /v1/file/list?parent_id=caf039566e9f11f0aa3a7e3f2c089143&keywords=&page_size=10&page=1 HTTP/1.1" 200 -
2025-08-01 06:21:49,954 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:49] "GET /v1/canvas/templates HTTP/1.1" 200 -
2025-08-01 06:21:49,967 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:49] "GET /v1/canvas/listteam?page=1&page_size=30&keywords= HTTP/1.1" 200 -
2025-08-01 06:21:50,969 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:50] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 06:21:50,974 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:50] "GET /v1/dialog/list HTTP/1.1" 200 -
2025-08-01 06:21:54,115 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:54] "POST /v1/kb/list HTTP/1.1" 200 -
2025-08-01 06:21:55,465 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:55] "GET /v1/canvas/templates HTTP/1.1" 200 -
2025-08-01 06:21:55,466 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:55] "GET /v1/canvas/listteam?page=1&page_size=30&keywords= HTTP/1.1" 200 -
2025-08-01 06:21:56,060 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:56] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 06:21:56,377 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:56] "POST /v1/kb/list?page=1&page_size=30&keywords= HTTP/1.1" 200 -
2025-08-01 06:21:57,688 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:57] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 06:21:57,690 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:57] "POST /v1/kb/list?page=1&page_size=30&keywords= HTTP/1.1" 200 -
2025-08-01 06:21:59,123 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:59] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 06:21:59,142 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:59] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 06:21:59,189 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:59] "GET /v1/tenant/list HTTP/1.1" 200 -
2025-08-01 06:21:59,356 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:59] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 06:21:59,358 INFO     25 172.18.0.1 - - [01/Aug/2025 06:21:59] "POST /v1/kb/list?page=1&page_size=30&keywords= HTTP/1.1" 200 -
2025-08-01 06:22:03,058 INFO     25 172.18.0.1 - - [01/Aug/2025 06:22:03] "GET /v1/canvas/listteam?page=1&page_size=30&keywords= HTTP/1.1" 200 -
2025-08-01 06:22:03,059 INFO     25 172.18.0.1 - - [01/Aug/2025 06:22:03] "GET /v1/canvas/templates HTTP/1.1" 200 -
2025-08-01 06:22:06,375 INFO     25 172.18.0.1 - - [01/Aug/2025 06:22:06] "GET /v1/system/version HTTP/1.1" 200 -
2025-08-01 06:22:06,688 INFO     25 172.18.0.1 - - [01/Aug/2025 06:22:06] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 06:22:10,695 INFO     25 172.18.0.1 - - [01/Aug/2025 06:22:10] "GET /v1/langfuse/api_key HTTP/1.1" 200 -
2025-08-01 06:22:16,333 INFO     25 GET http://es01:9200/_cluster/health [status:200 duration:0.106s]
2025-08-01 06:22:16,389 INFO     25 172.18.0.1 - - [01/Aug/2025 06:22:16] "GET /v1/system/status HTTP/1.1" 200 -
2025-08-01 06:22:16,870 INFO     25 172.18.0.1 - - [01/Aug/2025 06:22:16] "GET /v1/llm/my_llms HTTP/1.1" 200 -
2025-08-01 06:22:16,875 INFO     25 172.18.0.1 - - [01/Aug/2025 06:22:16] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 06:22:16,897 INFO     25 172.18.0.1 - - [01/Aug/2025 06:22:16] "GET /v1/llm/factories HTTP/1.1" 200 -
2025-08-01 06:22:17,282 INFO     25 GET http://es01:9200/_cluster/health [status:200 duration:0.004s]
2025-08-01 06:22:17,296 INFO     25 172.18.0.1 - - [01/Aug/2025 06:22:17] "GET /v1/system/status HTTP/1.1" 200 -
2025-08-01 06:22:18,284 INFO     25 172.18.0.1 - - [01/Aug/2025 06:22:18] "GET /v1/langfuse/api_key HTTP/1.1" 200 -
2025-08-01 06:23:10,946 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:10] "GET /v1/tenant/c6a3ddbc6e9f11f099317e3f2c089143/user/list HTTP/1.1" 200 -
2025-08-01 06:23:10,946 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:10] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 06:23:11,255 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:11] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 06:23:11,256 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:11] "GET /v1/tenant/list HTTP/1.1" 200 -
2025-08-01 06:23:11,839 INFO     25 GET http://es01:9200/_cluster/health [status:200 duration:0.003s]
2025-08-01 06:23:11,850 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:11] "GET /v1/system/status HTTP/1.1" 200 -
2025-08-01 06:23:15,201 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:15] "GET /v1/llm/my_llms HTTP/1.1" 200 -
2025-08-01 06:23:15,207 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:15] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 06:23:15,226 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:15] "GET /v1/llm/factories HTTP/1.1" 200 -
2025-08-01 06:23:17,211 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:17] "GET /v1/llm/my_llms HTTP/1.1" 200 -
2025-08-01 06:23:17,211 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:17] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 06:23:17,232 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:17] "GET /v1/llm/factories HTTP/1.1" 200 -
2025-08-01 06:23:30,232 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:30] "GET /v1/tenant/c6a3ddbc6e9f11f099317e3f2c089143/user/list HTTP/1.1" 200 -
2025-08-01 06:23:30,233 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:30] "GET /v1/tenant/list HTTP/1.1" 200 -
2025-08-01 06:23:30,233 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:30] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 06:23:30,515 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:30] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 06:23:31,570 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:31] "GET /v1/langfuse/api_key HTTP/1.1" 200 -
2025-08-01 06:23:31,571 INFO     25 GET http://es01:9200/_cluster/health [status:200 duration:0.004s]
2025-08-01 06:23:31,583 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:31] "GET /v1/system/status HTTP/1.1" 200 -
2025-08-01 06:23:38,362 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:38] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 06:23:39,522 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:39] "GET /v1/langfuse/api_key HTTP/1.1" 200 -
2025-08-01 06:23:39,802 INFO     25 GET http://es01:9200/_cluster/health [status:200 duration:0.005s]
2025-08-01 06:23:39,813 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:39] "GET /v1/system/status HTTP/1.1" 200 -
2025-08-01 06:23:40,395 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:40] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 06:23:40,404 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:40] "GET /v1/tenant/c6a3ddbc6e9f11f099317e3f2c089143/user/list HTTP/1.1" 200 -
2025-08-01 06:23:40,404 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:40] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 06:23:40,407 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:40] "GET /v1/tenant/list HTTP/1.1" 200 -
2025-08-01 06:23:42,412 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:42] "GET /v1/llm/my_llms HTTP/1.1" 200 -
2025-08-01 06:23:42,419 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:42] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 06:23:42,439 INFO     25 172.18.0.1 - - [01/Aug/2025 06:23:42] "GET /v1/llm/factories HTTP/1.1" 200 -
2025-08-01 06:27:39,095 INFO     25 172.18.0.1 - - [01/Aug/2025 06:27:39] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 06:27:39,103 INFO     25 172.18.0.1 - - [01/Aug/2025 06:27:39] "GET /v1/tenant/list HTTP/1.1" 200 -
2025-08-01 06:27:39,105 INFO     25 172.18.0.1 - - [01/Aug/2025 06:27:39] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 06:27:39,115 INFO     25 172.18.0.1 - - [01/Aug/2025 06:27:39] "GET /v1/llm/my_llms HTTP/1.1" 200 -
2025-08-01 06:27:39,135 INFO     25 172.18.0.1 - - [01/Aug/2025 06:27:39] "GET /v1/llm/factories HTTP/1.1" 200 -
2025-08-01 06:32:29,088 INFO     25 172.18.0.1 - - [01/Aug/2025 06:32:29] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 06:32:29,097 INFO     25 172.18.0.1 - - [01/Aug/2025 06:32:29] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 06:32:29,099 INFO     25 172.18.0.1 - - [01/Aug/2025 06:32:29] "GET /v1/tenant/list HTTP/1.1" 200 -
2025-08-01 06:32:29,100 INFO     25 172.18.0.1 - - [01/Aug/2025 06:32:29] "GET /v1/llm/my_llms HTTP/1.1" 200 -
2025-08-01 06:32:29,121 INFO     25 172.18.0.1 - - [01/Aug/2025 06:32:29] "GET /v1/llm/factories HTTP/1.1" 200 -
2025-08-01 06:32:44,286 INFO     25 HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 06:32:44,474 INFO     25 172.18.0.1 - - [01/Aug/2025 06:32:44] "POST /v1/llm/set_api_key HTTP/1.1" 200 -
2025-08-01 06:32:44,807 INFO     25 172.18.0.1 - - [01/Aug/2025 06:32:44] "GET /v1/llm/my_llms HTTP/1.1" 200 -
2025-08-01 06:32:44,824 INFO     25 172.18.0.1 - - [01/Aug/2025 06:32:44] "GET /v1/llm/factories HTTP/1.1" 200 -
2025-08-01 06:32:53,751 INFO     25 172.18.0.1 - - [01/Aug/2025 06:32:53] "POST /v1/llm/delete_llm HTTP/1.1" 200 -
2025-08-01 06:32:54,095 INFO     25 172.18.0.1 - - [01/Aug/2025 06:32:54] "GET /v1/llm/my_llms HTTP/1.1" 200 -
2025-08-01 06:32:54,124 INFO     25 172.18.0.1 - - [01/Aug/2025 06:32:54] "GET /v1/llm/factories HTTP/1.1" 200 -
2025-08-01 06:33:03,695 INFO     25 172.18.0.1 - - [01/Aug/2025 06:33:03] "POST /v1/llm/delete_llm HTTP/1.1" 200 -
2025-08-01 06:33:04,063 INFO     25 172.18.0.1 - - [01/Aug/2025 06:33:04] "GET /v1/llm/my_llms HTTP/1.1" 200 -
2025-08-01 06:33:04,085 INFO     25 172.18.0.1 - - [01/Aug/2025 06:33:04] "GET /v1/llm/factories HTTP/1.1" 200 -
2025-08-01 06:33:13,837 INFO     25 172.18.0.1 - - [01/Aug/2025 06:33:13] "GET /v1/llm/list HTTP/1.1" 200 -
2025-08-01 06:33:14,121 INFO     25 172.18.0.1 - - [01/Aug/2025 06:33:14] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 06:33:35,689 INFO     25 172.18.0.1 - - [01/Aug/2025 06:33:35] "POST /v1/user/set_tenant_info HTTP/1.1" 200 -
2025-08-01 06:33:38,420 INFO     25 172.18.0.1 - - [01/Aug/2025 06:33:38] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 06:33:41,753 INFO     25 172.18.0.1 - - [01/Aug/2025 06:33:41] "GET /v1/langfuse/api_key HTTP/1.1" 200 -
2025-08-01 06:33:42,012 INFO     25 GET http://es01:9200/_cluster/health [status:200 duration:0.008s]
2025-08-01 06:33:42,026 INFO     25 172.18.0.1 - - [01/Aug/2025 06:33:42] "GET /v1/system/status HTTP/1.1" 200 -
2025-08-01 06:33:44,010 INFO     25 172.18.0.1 - - [01/Aug/2025 06:33:44] "POST /v1/kb/list HTTP/1.1" 200 -
2025-08-01 06:33:46,405 INFO     25 172.18.0.1 - - [01/Aug/2025 06:33:46] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 06:33:46,406 INFO     25 172.18.0.1 - - [01/Aug/2025 06:33:46] "GET /v1/dialog/list HTTP/1.1" 200 -
2025-08-01 06:33:46,904 INFO     25 172.18.0.1 - - [01/Aug/2025 06:33:46] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 06:33:46,906 INFO     25 172.18.0.1 - - [01/Aug/2025 06:33:46] "POST /v1/kb/list?page=1&page_size=30&keywords= HTTP/1.1" 200 -
2025-08-01 06:50:48,640 INFO     25 172.18.0.1 - - [01/Aug/2025 06:50:48] "POST /v1/kb/create HTTP/1.1" 200 -
2025-08-01 06:50:49,024 INFO     25 172.18.0.1 - - [01/Aug/2025 06:50:49] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 06:50:49,054 INFO     25 172.18.0.1 - - [01/Aug/2025 06:50:49] "GET /v1/kb/detail?kb_id=db4a96e46ea311f092697e3f2c089143 HTTP/1.1" 200 -
2025-08-01 06:50:49,111 INFO     25 HEAD http://es01:9200/ragflow_c6a3ddbc6e9f11f099317e3f2c089143 [status:404 duration:0.065s]
2025-08-01 06:50:49,113 INFO     25 172.18.0.1 - - [01/Aug/2025 06:50:49] "GET /v1/kb/db4a96e46ea311f092697e3f2c089143/knowledge_graph HTTP/1.1" 200 -
2025-08-01 06:50:49,237 INFO     25 172.18.0.1 - - [01/Aug/2025 06:50:49] "POST /v1/kb/list HTTP/1.1" 200 -
2025-08-01 06:50:49,258 INFO     25 172.18.0.1 - - [01/Aug/2025 06:50:49] "GET /v1/llm/list HTTP/1.1" 200 -
2025-08-01 06:50:49,315 INFO     25 172.18.0.1 - - [01/Aug/2025 06:50:49] "GET /v1/kb/detail?kb_id=db4a96e46ea311f092697e3f2c089143 HTTP/1.1" 200 -
2025-08-01 06:50:49,511 INFO     25 172.18.0.1 - - [01/Aug/2025 06:50:49] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 06:50:55,726 INFO     25 172.18.0.1 - - [01/Aug/2025 06:50:55] "GET /v1/system/version HTTP/1.1" 200 -
2025-08-01 06:50:56,056 INFO     25 172.18.0.1 - - [01/Aug/2025 06:50:56] "GET /v1/llm/my_llms HTTP/1.1" 200 -
2025-08-01 06:50:56,057 INFO     25 172.18.0.1 - - [01/Aug/2025 06:50:56] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 06:50:56,075 INFO     25 172.18.0.1 - - [01/Aug/2025 06:50:56] "GET /v1/llm/factories HTTP/1.1" 200 -
2025-08-01 06:51:08,846 INFO     25 172.18.0.1 - - [01/Aug/2025 06:51:08] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 06:51:08,871 INFO     25 172.18.0.1 - - [01/Aug/2025 06:51:08] "GET /v1/llm/list HTTP/1.1" 200 -
2025-08-01 06:51:17,649 INFO     25 172.18.0.1 - - [01/Aug/2025 06:51:17] "POST /v1/user/set_tenant_info HTTP/1.1" 200 -
2025-08-01 06:51:20,636 INFO     25 172.18.0.1 - - [01/Aug/2025 06:51:20] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 06:51:20,638 INFO     25 172.18.0.1 - - [01/Aug/2025 06:51:20] "POST /v1/kb/list?page=1&page_size=30&keywords= HTTP/1.1" 200 -
2025-08-01 06:51:20,980 INFO     25 172.18.0.1 - - [01/Aug/2025 06:51:20] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 06:51:23,676 INFO     25 172.18.0.1 - - [01/Aug/2025 06:51:23] "GET /v1/kb/detail?kb_id=db4a96e46ea311f092697e3f2c089143 HTTP/1.1" 200 -
2025-08-01 06:51:23,984 INFO     25 HEAD http://es01:9200/ragflow_c6a3ddbc6e9f11f099317e3f2c089143 [status:404 duration:0.004s]
2025-08-01 06:51:23,985 INFO     25 172.18.0.1 - - [01/Aug/2025 06:51:23] "GET /v1/kb/db4a96e46ea311f092697e3f2c089143/knowledge_graph HTTP/1.1" 200 -
2025-08-01 06:51:24,040 INFO     25 172.18.0.1 - - [01/Aug/2025 06:51:24] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 06:51:24,049 INFO     25 172.18.0.1 - - [01/Aug/2025 06:51:24] "POST /v1/document/list?kb_id=db4a96e46ea311f092697e3f2c089143&keywords=&page_size=10&page=1 HTTP/1.1" 200 -
2025-08-01 06:51:24,050 INFO     25 172.18.0.1 - - [01/Aug/2025 06:51:24] "GET /v1/kb/detail?kb_id=db4a96e46ea311f092697e3f2c089143 HTTP/1.1" 200 -
2025-08-01 07:03:34,570 INFO     25 172.18.0.1 - - [01/Aug/2025 07:03:34] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 07:03:34,571 INFO     25 172.18.0.1 - - [01/Aug/2025 07:03:34] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 07:03:34,642 INFO     25 172.18.0.1 - - [01/Aug/2025 07:03:34] "GET /v1/tenant/list HTTP/1.1" 200 -
2025-08-01 07:03:34,760 INFO     25 172.18.0.1 - - [01/Aug/2025 07:03:34] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 07:03:34,765 INFO     25 172.18.0.1 - - [01/Aug/2025 07:03:34] "POST /v1/kb/list?page=1&page_size=30&keywords= HTTP/1.1" 200 -
2025-08-01 07:03:34,806 INFO     25 172.18.0.1 - - [01/Aug/2025 07:03:34] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 07:03:36,985 INFO     25 172.18.0.1 - - [01/Aug/2025 07:03:36] "POST /v1/document/list?kb_id=db4a96e46ea311f092697e3f2c089143&keywords=&page_size=10&page=1 HTTP/1.1" 200 -
2025-08-01 07:03:36,986 INFO     25 172.18.0.1 - - [01/Aug/2025 07:03:36] "GET /v1/kb/detail?kb_id=db4a96e46ea311f092697e3f2c089143 HTTP/1.1" 200 -
2025-08-01 07:03:36,987 INFO     25 HEAD http://es01:9200/ragflow_c6a3ddbc6e9f11f099317e3f2c089143 [status:404 duration:0.006s]
2025-08-01 07:03:36,989 INFO     25 172.18.0.1 - - [01/Aug/2025 07:03:36] "GET /v1/kb/db4a96e46ea311f092697e3f2c089143/knowledge_graph HTTP/1.1" 200 -
2025-08-01 07:03:37,181 INFO     25 172.18.0.1 - - [01/Aug/2025 07:03:37] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 07:03:50,669 INFO     25 172.18.0.1 - - [01/Aug/2025 07:03:50] "POST /v1/document/list?kb_id=db4a96e46ea311f092697e3f2c089143&keywords=&page_size=10&page=1 HTTP/1.1" 200 -
2025-08-01 07:03:50,985 INFO     25 172.18.0.1 - - [01/Aug/2025 07:03:50] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 07:03:51,002 INFO     25 172.18.0.1 - - [01/Aug/2025 07:03:51] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 07:03:51,004 INFO     25 172.18.0.1 - - [01/Aug/2025 07:03:51] "GET /v1/tenant/list HTTP/1.1" 200 -
2025-08-01 07:03:51,015 INFO     25 172.18.0.1 - - [01/Aug/2025 07:03:51] "GET /v1/kb/detail?kb_id=db4a96e46ea311f092697e3f2c089143 HTTP/1.1" 200 -
2025-08-01 07:03:51,016 INFO     25 HEAD http://es01:9200/ragflow_c6a3ddbc6e9f11f099317e3f2c089143 [status:404 duration:0.004s]
2025-08-01 07:03:51,018 INFO     25 172.18.0.1 - - [01/Aug/2025 07:03:51] "GET /v1/kb/db4a96e46ea311f092697e3f2c089143/knowledge_graph HTTP/1.1" 200 -
2025-08-01 07:05:13,627 INFO     25 172.18.0.1 - - [01/Aug/2025 07:05:13] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 07:05:13,643 INFO     25 172.18.0.1 - - [01/Aug/2025 07:05:13] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 07:05:13,646 INFO     25 172.18.0.1 - - [01/Aug/2025 07:05:13] "GET /v1/tenant/list HTTP/1.1" 200 -
2025-08-01 07:05:13,660 INFO     25 HEAD http://es01:9200/ragflow_c6a3ddbc6e9f11f099317e3f2c089143 [status:404 duration:0.004s]
2025-08-01 07:05:13,666 INFO     25 172.18.0.1 - - [01/Aug/2025 07:05:13] "GET /v1/kb/db4a96e46ea311f092697e3f2c089143/knowledge_graph HTTP/1.1" 200 -
2025-08-01 07:05:13,666 INFO     25 172.18.0.1 - - [01/Aug/2025 07:05:13] "GET /v1/kb/detail?kb_id=db4a96e46ea311f092697e3f2c089143 HTTP/1.1" 200 -
2025-08-01 07:05:13,667 INFO     25 172.18.0.1 - - [01/Aug/2025 07:05:13] "POST /v1/document/list?kb_id=db4a96e46ea311f092697e3f2c089143&keywords=&page_size=10&page=1 HTTP/1.1" 200 -
2025-08-01 07:46:04,230 INFO     25 172.18.0.1 - - [01/Aug/2025 07:46:04] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 07:46:04,237 INFO     25 172.18.0.1 - - [01/Aug/2025 07:46:04] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 07:46:04,246 INFO     25 172.18.0.1 - - [01/Aug/2025 07:46:04] "POST /v1/kb/list?page=1&page_size=30&keywords= HTTP/1.1" 200 -
2025-08-01 07:46:04,297 INFO     25 172.18.0.1 - - [01/Aug/2025 07:46:04] "GET /v1/tenant/list HTTP/1.1" 200 -
2025-08-01 07:46:04,298 INFO     25 172.18.0.1 - - [01/Aug/2025 07:46:04] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 07:46:05,899 INFO     25 172.18.0.1 - - [01/Aug/2025 07:46:05] "GET /v1/kb/detail?kb_id=db4a96e46ea311f092697e3f2c089143 HTTP/1.1" 200 -
2025-08-01 07:46:05,901 INFO     25 172.18.0.1 - - [01/Aug/2025 07:46:05] "POST /v1/document/list?kb_id=db4a96e46ea311f092697e3f2c089143&keywords=&page_size=10&page=1 HTTP/1.1" 200 -
2025-08-01 07:46:05,955 INFO     25 HEAD http://es01:9200/ragflow_c6a3ddbc6e9f11f099317e3f2c089143 [status:404 duration:0.076s]
2025-08-01 07:46:05,958 INFO     25 172.18.0.1 - - [01/Aug/2025 07:46:05] "GET /v1/kb/db4a96e46ea311f092697e3f2c089143/knowledge_graph HTTP/1.1" 200 -
2025-08-01 07:46:06,098 INFO     25 172.18.0.1 - - [01/Aug/2025 07:46:06] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 07:46:20,926 INFO     25 172.18.0.1 - - [01/Aug/2025 07:46:20] "POST /v1/document/list?kb_id=db4a96e46ea311f092697e3f2c089143&keywords=&page_size=10&page=1 HTTP/1.1" 200 -
2025-08-01 07:46:36,028 INFO     25 172.18.0.1 - - [01/Aug/2025 07:46:36] "POST /v1/document/list?kb_id=db4a96e46ea311f092697e3f2c089143&keywords=&page_size=10&page=1 HTTP/1.1" 200 -
2025-08-01 07:46:36,112 INFO     25 172.18.0.1 - - [01/Aug/2025 07:46:36] "POST /v1/document/upload HTTP/1.1" 200 -
2025-08-01 07:46:36,268 INFO     25 172.18.0.1 - - [01/Aug/2025 07:46:36] "POST /v1/document/list?kb_id=db4a96e46ea311f092697e3f2c089143&keywords=&page_size=10&page=1 HTTP/1.1" 200 -
2025-08-01 07:46:36,442 INFO     25 172.18.0.1 - - [01/Aug/2025 07:46:36] "POST /v1/document/list?kb_id=db4a96e46ea311f092697e3f2c089143&keywords=&page_size=10&page=1 HTTP/1.1" 200 -
2025-08-01 07:46:36,523 INFO     25 172.18.0.1 - - [01/Aug/2025 07:46:36] "POST /v1/document/run HTTP/1.1" 200 -
2025-08-01 07:46:36,550 INFO     25 172.18.0.1 - - [01/Aug/2025 07:46:36] "POST /v1/document/list?kb_id=db4a96e46ea311f092697e3f2c089143&keywords=&page_size=10&page=1 HTTP/1.1" 200 -
2025-08-01 07:46:51,917 INFO     25 172.18.0.1 - - [01/Aug/2025 07:46:51] "POST /v1/document/list?kb_id=db4a96e46ea311f092697e3f2c089143&keywords=&page_size=10&page=1 HTTP/1.1" 200 -
2025-08-01 07:47:06,941 INFO     25 172.18.0.1 - - [01/Aug/2025 07:47:06] "POST /v1/document/list?kb_id=db4a96e46ea311f092697e3f2c089143&keywords=&page_size=10&page=1 HTTP/1.1" 200 -
2025-08-01 07:47:22,292 INFO     25 172.18.0.1 - - [01/Aug/2025 07:47:22] "POST /v1/document/list?kb_id=db4a96e46ea311f092697e3f2c089143&keywords=&page_size=10&page=1 HTTP/1.1" 200 -
2025-08-01 07:47:28,398 INFO     25 172.18.0.1 - - [01/Aug/2025 07:47:28] "POST /v1/document/list?kb_id=db4a96e46ea311f092697e3f2c089143&keywords=&page_size=10&page=1 HTTP/1.1" 200 -
2025-08-01 07:47:28,717 INFO     25 172.18.0.1 - - [01/Aug/2025 07:47:28] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 07:47:28,737 INFO     25 172.18.0.1 - - [01/Aug/2025 07:47:28] "GET /v1/tenant/list HTTP/1.1" 200 -
2025-08-01 07:47:28,741 INFO     25 172.18.0.1 - - [01/Aug/2025 07:47:28] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 07:47:28,748 INFO     25 172.18.0.1 - - [01/Aug/2025 07:47:28] "GET /v1/kb/detail?kb_id=db4a96e46ea311f092697e3f2c089143 HTTP/1.1" 200 -
2025-08-01 07:47:28,755 INFO     25 HEAD http://es01:9200/ragflow_c6a3ddbc6e9f11f099317e3f2c089143 [status:200 duration:0.019s]
2025-08-01 07:47:30,846 INFO     25 POST http://es01:9200/ragflow_c6a3ddbc6e9f11f099317e3f2c089143/_search [status:200 duration:0.171s]
2025-08-01 07:47:30,851 INFO     25 172.18.0.1 - - [01/Aug/2025 07:47:30] "GET /v1/kb/db4a96e46ea311f092697e3f2c089143/knowledge_graph HTTP/1.1" 200 -
2025-08-01 07:51:53,880 ERROR    25 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 854, in dispatch_request
    self.raise_routing_exception(req)
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 463, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "/ragflow/.venv/lib/python3.10/site-packages/werkzeug/routing/map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-08-01 07:51:53,886 INFO     25 172.18.0.1 - - [01/Aug/2025 07:51:53] "GET /v1/datasets HTTP/1.1" 200 -
2025-08-01 07:52:01,242 INFO     25 172.18.0.1 - - [01/Aug/2025 07:52:01] "GET /api/v1/datasets HTTP/1.1" 200 -
2025-08-01 07:52:08,302 ERROR    25 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 854, in dispatch_request
    self.raise_routing_exception(req)
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 463, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "/ragflow/.venv/lib/python3.10/site-packages/werkzeug/routing/map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-08-01 07:52:08,303 INFO     25 172.18.0.1 - - [01/Aug/2025 07:52:08] "GET /api/v1/user/login HTTP/1.1" 200 -
2025-08-01 07:52:57,730 ERROR    25 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 854, in dispatch_request
    self.raise_routing_exception(req)
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 463, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "/ragflow/.venv/lib/python3.10/site-packages/werkzeug/routing/map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-08-01 07:52:57,732 INFO     25 172.18.0.1 - - [01/Aug/2025 07:52:57] "POST /api/v1/user/login HTTP/1.1" 200 -
2025-08-01 07:53:02,573 ERROR    25 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 854, in dispatch_request
    self.raise_routing_exception(req)
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 463, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "/ragflow/.venv/lib/python3.10/site-packages/werkzeug/routing/map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-08-01 07:53:02,575 INFO     25 172.18.0.1 - - [01/Aug/2025 07:53:02] "GET / HTTP/1.1" 200 -
2025-08-01 07:53:02,963 ERROR    25 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 854, in dispatch_request
    self.raise_routing_exception(req)
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 463, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "/ragflow/.venv/lib/python3.10/site-packages/werkzeug/routing/map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-08-01 07:53:02,964 INFO     25 172.18.0.1 - - [01/Aug/2025 07:53:02] "GET /favicon.ico HTTP/1.1" 200 -
2025-08-01 07:53:39,291 ERROR    25 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 854, in dispatch_request
    self.raise_routing_exception(req)
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/app.py", line 463, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "/ragflow/.venv/lib/python3.10/site-packages/flask/ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "/ragflow/.venv/lib/python3.10/site-packages/werkzeug/routing/map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-08-01 07:53:39,292 INFO     25 172.18.0.1 - - [01/Aug/2025 07:53:39] "POST /api/v1/user/login HTTP/1.1" 200 -
2025-08-01 07:54:11,224 INFO     25 172.18.0.1 - - [01/Aug/2025 07:54:11] "POST /v1/user/login HTTP/1.1" 200 -
2025-08-01 07:55:00,035 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:00] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 07:55:00,048 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:00] "GET /v1/tenant/list HTTP/1.1" 200 -
2025-08-01 07:55:00,048 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:00] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 07:55:00,063 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:00] "POST /v1/document/list?kb_id=db4a96e46ea311f092697e3f2c089143&keywords=&page_size=10&page=1 HTTP/1.1" 200 -
2025-08-01 07:55:00,063 INFO     25 HEAD http://es01:9200/ragflow_c6a3ddbc6e9f11f099317e3f2c089143 [status:200 duration:0.006s]
2025-08-01 07:55:00,067 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:00] "GET /v1/kb/detail?kb_id=db4a96e46ea311f092697e3f2c089143 HTTP/1.1" 200 -
2025-08-01 07:55:00,237 INFO     25 POST http://es01:9200/ragflow_c6a3ddbc6e9f11f099317e3f2c089143/_search [status:200 duration:0.018s]
2025-08-01 07:55:00,239 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:00] "GET /v1/kb/db4a96e46ea311f092697e3f2c089143/knowledge_graph HTTP/1.1" 200 -
2025-08-01 07:55:03,024 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:03] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 07:55:03,026 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:03] "GET /v1/kb/detail?kb_id=db4a96e46ea311f092697e3f2c089143 HTTP/1.1" 200 -
2025-08-01 07:55:03,256 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:03] "POST /v1/kb/list HTTP/1.1" 200 -
2025-08-01 07:55:03,311 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:03] "GET /v1/llm/list HTTP/1.1" 200 -
2025-08-01 07:55:04,187 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:04] "GET /v1/llm/list HTTP/1.1" 200 -
2025-08-01 07:55:05,039 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:05] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 07:55:05,047 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:05] "GET /v1/kb/detail?kb_id=db4a96e46ea311f092697e3f2c089143 HTTP/1.1" 200 -
2025-08-01 07:55:05,049 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:05] "POST /v1/document/list?kb_id=db4a96e46ea311f092697e3f2c089143&keywords=&page_size=10&page=1 HTTP/1.1" 200 -
2025-08-01 07:55:14,787 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:14] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 07:55:14,791 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:14] "GET /v1/user/tenant_info HTTP/1.1" 200 -
2025-08-01 07:55:14,839 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:14] "GET /v1/tenant/list HTTP/1.1" 200 -
2025-08-01 07:55:14,947 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:14] "POST /v1/kb/list?page=1&page_size=30&keywords= HTTP/1.1" 200 -
2025-08-01 07:55:14,957 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:14] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 07:55:18,709 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:18] "GET /v1/system/version HTTP/1.1" 200 -
2025-08-01 07:55:18,801 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:18] "GET /v1/user/info HTTP/1.1" 200 -
2025-08-01 07:55:22,994 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:22] "GET /v1/langfuse/api_key HTTP/1.1" 200 -
2025-08-01 07:55:38,247 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:38] "GET /v1/system/token_list HTTP/1.1" 200 -
2025-08-01 07:55:40,131 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:40] "POST /v1/system/new_token HTTP/1.1" 200 -
2025-08-01 07:55:40,155 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:40] "GET /v1/system/token_list HTTP/1.1" 200 -
2025-08-01 07:55:53,889 INFO     25 172.18.0.1 - - [01/Aug/2025 07:55:53] "GET /v1/system/token_list HTTP/1.1" 200 -
2025-08-01 07:58:03,029 INFO     25 172.18.0.1 - - [01/Aug/2025 07:58:03] "GET /api/v1/datasets HTTP/1.1" 200 -
2025-08-01 07:58:37,141 INFO     25 172.18.0.1 - - [01/Aug/2025 07:58:37] "GET /api/v1/datasets HTTP/1.1" 200 -
2025-08-01 07:58:48,758 INFO     25 172.18.0.1 - - [01/Aug/2025 07:58:48] "GET /api/v1/datasets HTTP/1.1" 200 -
2025-08-01 07:59:01,897 INFO     25 172.18.0.1 - - [01/Aug/2025 07:59:01] "POST /api/v1/retrieval HTTP/1.1" 200 -
2025-08-01 08:00:08,608 INFO     25 POST http://es01:9200/ragflow_c6a3ddbc6e9f11f099317e3f2c089143/_search [status:200 duration:0.619s]
2025-08-01 08:00:09,310 INFO     25 172.18.0.1 - - [01/Aug/2025 08:00:09] "POST /api/v1/retrieval HTTP/1.1" 200 -
2025-08-01 08:00:22,012 INFO     25 172.18.0.1 - - [01/Aug/2025 08:00:22] "GET /api/v1/datasets HTTP/1.1" 200 -
2025-08-01 08:00:22,634 INFO     25 POST http://es01:9200/ragflow_c6a3ddbc6e9f11f099317e3f2c089143/_search [status:200 duration:0.336s]
2025-08-01 08:00:22,852 INFO     25 172.18.0.1 - - [01/Aug/2025 08:00:22] "POST /api/v1/retrieval HTTP/1.1" 200 -
